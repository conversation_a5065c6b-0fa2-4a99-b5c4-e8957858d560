const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  // 邮箱生成
  generateEmail: (options) => ipcRenderer.invoke('generate-email', options),
  
  // 邮件监控
  startEmailMonitoring: (emailAddress) => ipcRenderer.invoke('start-email-monitoring', emailAddress),
  stopEmailMonitoring: () => ipcRenderer.invoke('stop-email-monitoring'),
  getEmails: () => ipcRenderer.invoke('get-emails'),
  
  // 验证码提取
  extractVerificationCode: (emailContent) => ipcRenderer.invoke('extract-verification-code', emailContent),
  
  // 事件监听
  setupEmailListener: () => ipcRenderer.send('setup-email-listener'),
  onNewEmail: (callback) => ipcRenderer.on('new-email-received', callback),
  onVerificationCode: (callback) => ipcRenderer.on('verification-code-extracted', callback),
  
  // 移除监听器
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});
