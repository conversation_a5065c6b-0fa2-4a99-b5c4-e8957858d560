class CodeExtractor {
  constructor() {
    // 验证码匹配模式
    this.patterns = [
      // 中文验证码模式
      {
        name: '中文验证码',
        regex: /验证码[：:\s]*([A-Z0-9]{4,8})/gi,
        group: 1
      },
      {
        name: '中文验证码2',
        regex: /验证码为[：:\s]*([A-Z0-9]{4,8})/gi,
        group: 1
      },
      {
        name: '中文验证码3',
        regex: /您的验证码是[：:\s]*([A-Z0-9]{4,8})/gi,
        group: 1
      },
      
      // 英文验证码模式
      {
        name: '英文验证码',
        regex: /verification code[：:\s]*([A-Z0-9]{4,8})/gi,
        group: 1
      },
      {
        name: '英文验证码2',
        regex: /code[：:\s]*([A-Z0-9]{4,8})/gi,
        group: 1
      },
      {
        name: '英文验证码3',
        regex: /your code is[：:\s]*([A-Z0-9]{4,8})/gi,
        group: 1
      },
      
      // 纯数字验证码
      {
        name: '6位数字',
        regex: /\b(\d{6})\b/g,
        group: 1
      },
      {
        name: '4位数字',
        regex: /\b(\d{4})\b/g,
        group: 1
      },
      {
        name: '5位数字',
        regex: /\b(\d{5})\b/g,
        group: 1
      },
      
      // 混合验证码
      {
        name: '4-8位混合',
        regex: /\b([A-Z0-9]{4,8})\b/g,
        group: 1
      },
      
      // 特殊格式
      {
        name: 'OTP格式',
        regex: /OTP[：:\s]*([A-Z0-9]{4,8})/gi,
        group: 1
      },
      {
        name: 'PIN格式',
        regex: /PIN[：:\s]*([A-Z0-9]{4,8})/gi,
        group: 1
      },
      
      // HTML中的验证码
      {
        name: 'HTML强调',
        regex: /<(?:strong|b|em)>([A-Z0-9]{4,8})<\/(?:strong|b|em)>/gi,
        group: 1
      },
      {
        name: 'HTML代码块',
        regex: /<code>([A-Z0-9]{4,8})<\/code>/gi,
        group: 1
      }
    ];

    // 排除的常见词汇（避免误识别）
    this.excludeWords = [
      'HTTP', 'HTTPS', 'HTML', 'JSON', 'XML', 'API',
      'URL', 'URI', 'UUID', 'GUID', 'MD5', 'SHA',
      'UTF8', 'ASCII', 'BASE64', 'JPEG', 'PNG',
      'PDF', 'DOC', 'XLS', 'PPT', 'ZIP', 'RAR',
      'YEAR', 'MONTH', 'DATE', 'TIME', 'HOUR',
      'ADMIN', 'USER', 'ROOT', 'TEST', 'DEMO'
    ];
  }

  extractCode(content) {
    try {
      if (!content || typeof content !== 'string') {
        return {
          success: false,
          error: '内容为空或格式不正确'
        };
      }

      // 清理内容
      const cleanContent = this.cleanContent(content);
      
      // 尝试各种模式匹配
      const candidates = [];
      
      for (const pattern of this.patterns) {
        const matches = this.findMatches(cleanContent, pattern);
        candidates.push(...matches);
      }

      if (candidates.length === 0) {
        return {
          success: false,
          error: '未找到验证码'
        };
      }

      // 筛选和排序候选验证码
      const bestCode = this.selectBestCode(candidates);

      if (bestCode) {
        return {
          success: true,
          code: bestCode.code,
          pattern: bestCode.pattern,
          confidence: bestCode.confidence
        };
      } else {
        return {
          success: false,
          error: '未找到有效的验证码'
        };
      }

    } catch (error) {
      console.error('提取验证码失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  cleanContent(content) {
    // 移除HTML标签但保留内容
    let cleaned = content.replace(/<[^>]*>/g, ' ');
    
    // 解码HTML实体
    cleaned = cleaned
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'");
    
    // 标准化空白字符
    cleaned = cleaned.replace(/\s+/g, ' ').trim();
    
    return cleaned;
  }

  findMatches(content, pattern) {
    const matches = [];
    let match;
    
    // 重置正则表达式的lastIndex
    pattern.regex.lastIndex = 0;
    
    while ((match = pattern.regex.exec(content)) !== null) {
      const code = match[pattern.group];
      
      if (this.isValidCode(code)) {
        matches.push({
          code: code,
          pattern: pattern.name,
          position: match.index,
          fullMatch: match[0]
        });
      }
      
      // 防止无限循环
      if (!pattern.regex.global) {
        break;
      }
    }
    
    return matches;
  }

  isValidCode(code) {
    if (!code || typeof code !== 'string') {
      return false;
    }

    // 长度检查
    if (code.length < 4 || code.length > 8) {
      return false;
    }

    // 排除常见词汇
    if (this.excludeWords.includes(code.toUpperCase())) {
      return false;
    }

    // 排除纯字母（除非是特定格式）
    if (/^[A-Z]+$/.test(code) && code.length > 6) {
      return false;
    }

    // 排除明显的时间格式
    if (/^\d{4}$/.test(code)) {
      const num = parseInt(code);
      if (num >= 1900 && num <= 2100) { // 可能是年份
        return false;
      }
      if (num >= 0 && num <= 2359) { // 可能是时间
        return false;
      }
    }

    return true;
  }

  selectBestCode(candidates) {
    if (candidates.length === 0) {
      return null;
    }

    // 计算每个候选验证码的置信度
    const scoredCandidates = candidates.map(candidate => {
      let confidence = 0;

      // 基于模式类型的得分
      if (candidate.pattern.includes('中文') || candidate.pattern.includes('英文')) {
        confidence += 50; // 明确的验证码标识
      } else if (candidate.pattern.includes('OTP') || candidate.pattern.includes('PIN')) {
        confidence += 40; // 特殊格式
      } else if (candidate.pattern.includes('HTML')) {
        confidence += 30; // HTML强调
      } else {
        confidence += 10; // 纯数字或混合
      }

      // 基于长度的得分
      if (candidate.code.length === 6) {
        confidence += 20; // 6位最常见
      } else if (candidate.code.length === 4) {
        confidence += 15; // 4位也很常见
      } else if (candidate.code.length === 5) {
        confidence += 10;
      }

      // 基于字符组成的得分
      if (/^\d+$/.test(candidate.code)) {
        confidence += 15; // 纯数字
      } else if (/^[A-Z0-9]+$/.test(candidate.code)) {
        confidence += 10; // 数字字母混合
      }

      // 基于位置的得分（越靠前越可能是验证码）
      const positionScore = Math.max(0, 10 - (candidate.position / 100));
      confidence += positionScore;

      return {
        ...candidate,
        confidence: confidence
      };
    });

    // 按置信度排序
    scoredCandidates.sort((a, b) => b.confidence - a.confidence);

    // 返回置信度最高的
    return scoredCandidates[0];
  }

  // 批量提取验证码（用于处理多封邮件）
  extractCodesFromMultiple(contents) {
    const results = [];
    
    for (const content of contents) {
      const result = this.extractCode(content);
      results.push(result);
    }
    
    return results;
  }

  // 获取所有可能的验证码（不只是最佳的）
  extractAllCodes(content) {
    try {
      const cleanContent = this.cleanContent(content);
      const allCandidates = [];
      
      for (const pattern of this.patterns) {
        const matches = this.findMatches(cleanContent, pattern);
        allCandidates.push(...matches);
      }

      // 去重
      const uniqueCodes = [];
      const seenCodes = new Set();
      
      for (const candidate of allCandidates) {
        if (!seenCodes.has(candidate.code)) {
          seenCodes.add(candidate.code);
          uniqueCodes.push(candidate);
        }
      }

      return {
        success: true,
        codes: uniqueCodes
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = CodeExtractor;
