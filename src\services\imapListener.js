const Imap = require('node-imap');
const { simpleParser } = require('mailparser');
const EventEmitter = require('events');
const fs = require('fs');
const path = require('path');

class ImapListener extends EventEmitter {
  constructor() {
    super();
    this.config = this.loadConfig();
    this.imap = null;
    this.isConnected = false;
    this.isMonitoring = false;
    this.currentEmail = '';
    this.processedEmails = new Set();
  }

  loadConfig() {
    try {
      const configPath = path.join(__dirname, '../../email_config.json');
      const configData = fs.readFileSync(configPath, 'utf8');
      return JSON.parse(configData);
    } catch (error) {
      console.error('加载配置文件失败:', error);
      return null;
    }
  }

  async startMonitoring(emailAddress) {
    try {
      if (!this.config || !this.config.custom_domain || !this.config.custom_domain.imap_settings) {
        throw new Error('IMAP配置不存在');
      }

      this.currentEmail = emailAddress;
      this.isMonitoring = true;
      this.processedEmails.clear();

      // 如果是自定义域名邮箱，使用IMAP连接
      if (emailAddress.includes(this.config.custom_domain.domain)) {
        await this.connectImap();
      }

      return {
        success: true,
        message: '开始监控邮箱'
      };
    } catch (error) {
      console.error('开始监控失败:', error);
      this.isMonitoring = false;
      return {
        success: false,
        error: error.message
      };
    }
  }

  async stopMonitoring() {
    try {
      this.isMonitoring = false;
      this.currentEmail = '';
      
      if (this.imap && this.isConnected) {
        this.imap.end();
        this.isConnected = false;
      }

      return {
        success: true,
        message: '停止监控邮箱'
      };
    } catch (error) {
      console.error('停止监控失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async connectImap() {
    return new Promise((resolve, reject) => {
      if (!this.config.custom_domain.imap_settings) {
        reject(new Error('IMAP设置不存在'));
        return;
      }

      const imapConfig = {
        user: this.config.custom_domain.imap_settings.username,
        password: this.config.custom_domain.imap_settings.password,
        host: this.config.custom_domain.imap_settings.server,
        port: this.config.custom_domain.imap_settings.port,
        tls: this.config.custom_domain.imap_settings.use_ssl,
        tlsOptions: {
          rejectUnauthorized: false
        }
      };

      this.imap = new Imap(imapConfig);

      this.imap.once('ready', () => {
        console.log('IMAP连接成功');
        this.isConnected = true;
        this.openInbox();
        resolve();
      });

      this.imap.once('error', (err) => {
        console.error('IMAP连接错误:', err);
        this.isConnected = false;
        reject(err);
      });

      this.imap.once('end', () => {
        console.log('IMAP连接结束');
        this.isConnected = false;
      });

      this.imap.connect();
    });
  }

  openInbox() {
    this.imap.openBox('INBOX', false, (err, box) => {
      if (err) {
        console.error('打开收件箱失败:', err);
        return;
      }

      console.log('收件箱打开成功');
      
      // 监听新邮件
      this.imap.on('mail', (numNewMsgs) => {
        console.log(`收到 ${numNewMsgs} 封新邮件`);
        this.fetchNewEmails();
      });

      // 立即检查现有邮件
      this.fetchNewEmails();
    });
  }

  fetchNewEmails() {
    if (!this.isConnected || !this.isMonitoring) {
      return;
    }

    // 搜索最近的邮件
    const searchCriteria = [
      'UNSEEN', // 未读邮件
      ['SINCE', new Date(Date.now() - 24 * 60 * 60 * 1000)] // 最近24小时
    ];

    this.imap.search(searchCriteria, (err, results) => {
      if (err) {
        console.error('搜索邮件失败:', err);
        return;
      }

      if (!results || results.length === 0) {
        console.log('没有找到新邮件');
        return;
      }

      console.log(`找到 ${results.length} 封邮件`);

      const fetch = this.imap.fetch(results, {
        bodies: '',
        markSeen: false
      });

      fetch.on('message', (msg, seqno) => {
        let emailData = {};

        msg.on('body', (stream, info) => {
          let buffer = '';
          stream.on('data', (chunk) => {
            buffer += chunk.toString('utf8');
          });

          stream.once('end', () => {
            this.parseEmail(buffer, seqno);
          });
        });

        msg.once('attributes', (attrs) => {
          emailData.attributes = attrs;
        });
      });

      fetch.once('error', (err) => {
        console.error('获取邮件失败:', err);
      });

      fetch.once('end', () => {
        console.log('邮件获取完成');
      });
    });
  }

  async parseEmail(rawEmail, seqno) {
    try {
      const parsed = await simpleParser(rawEmail);
      
      // 检查邮件是否与当前监控的邮箱相关
      if (!this.isEmailRelevant(parsed)) {
        return;
      }

      // 避免重复处理
      const emailId = `${seqno}-${parsed.messageId}`;
      if (this.processedEmails.has(emailId)) {
        return;
      }
      this.processedEmails.add(emailId);

      const email = {
        id: emailId,
        subject: parsed.subject || '无主题',
        from: parsed.from?.text || '未知发件人',
        to: parsed.to?.text || '',
        date: parsed.date || new Date(),
        text: parsed.text || '',
        html: parsed.html || '',
        seqno: seqno
      };

      console.log('解析到新邮件:', email.subject);

      // 发送新邮件事件
      this.emit('new-email', email);

      // 尝试提取验证码
      const CodeExtractor = require('./codeExtractor');
      const codeExtractor = new CodeExtractor();
      const codeResult = codeExtractor.extractCode(email.text || email.html);
      
      if (codeResult.success && codeResult.code) {
        console.log('提取到验证码:', codeResult.code);
        email.verificationCode = codeResult.code;
        this.emit('verification-code', codeResult.code);
      }

    } catch (error) {
      console.error('解析邮件失败:', error);
    }
  }

  isEmailRelevant(parsed) {
    if (!this.currentEmail) {
      return true;
    }

    // 检查收件人是否包含当前监控的邮箱
    const toAddresses = parsed.to?.text || '';
    const ccAddresses = parsed.cc?.text || '';
    const bccAddresses = parsed.bcc?.text || '';
    
    const allRecipients = `${toAddresses} ${ccAddresses} ${bccAddresses}`.toLowerCase();
    
    return allRecipients.includes(this.currentEmail.toLowerCase());
  }

  async getEmails() {
    try {
      // 这里可以返回已处理的邮件列表
      // 或者重新获取邮件
      return {
        success: true,
        emails: [] // 实际实现中应该返回邮件列表
      };
    } catch (error) {
      console.error('获取邮件失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = ImapListener;
