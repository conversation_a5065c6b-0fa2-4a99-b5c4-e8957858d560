# 临时邮箱生成器

一个功能强大的桌面临时邮箱生成工具，支持多种邮箱提供商，具备自动验证码提取功能。

## 功能特性

- 🔄 **多种邮箱类型**：支持 Mail.tm 和自定义域名邮箱
- 📧 **批量生成**：一次生成多个临时邮箱
- 📬 **实时监控**：自动监听新邮件到达
- 🔍 **智能提取**：自动识别并提取验证码
- 📋 **一键复制**：快速复制邮箱地址和验证码
- 🎨 **美观界面**：现代化的用户界面设计
- ⚡ **实时更新**：邮件状态实时刷新

## 安装和运行

### 前置要求

- Node.js 16.0 或更高版本
- npm 或 yarn 包管理器

### 安装依赖

```bash
npm install
```

### 开发模式运行

```bash
npm run dev
```

### 构建应用

```bash
# 构建所有平台
npm run build

# 构建 Windows 版本
npm run build-win

# 构建 macOS 版本
npm run build-mac

# 构建 Linux 版本
npm run build-linux
```

## 配置说明

应用使用 `email_config.json` 文件进行配置：

### Mail.tm 配置

Mail.tm 是一个免费的临时邮箱服务，无需额外配置即可使用。

### 自定义域名配置

如果您有自己的域名和邮箱服务，可以配置自定义域名：

```json
{
  "custom_domain": {
    "domain": "your-domain.com",
    "real_email": "<EMAIL>",
    "forwarding_service": "cloudflare",
    "imap_settings": {
      "server": "imap.gmail.com",
      "port": 993,
      "use_ssl": true,
      "username": "<EMAIL>",
      "password": "your-app-password",
      "check_interval": 30,
      "search_days": 1,
      "max_emails": 10
    }
  }
}
```

### IMAP 设置说明

- **server**: IMAP 服务器地址
- **port**: IMAP 端口（通常是 993 for SSL）
- **use_ssl**: 是否使用 SSL 连接
- **username**: 邮箱用户名
- **password**: 邮箱密码（建议使用应用专用密码）
- **check_interval**: 检查邮件的间隔（秒）
- **search_days**: 搜索最近几天的邮件
- **max_emails**: 最大邮件数量

## 使用方法

1. **选择邮箱类型**：在界面上选择 Mail.tm 或自定义域名
2. **设置生成数量**：选择要生成的邮箱数量（1-10个）
3. **生成邮箱**：点击"生成随机邮箱"按钮
4. **开始监控**：点击邮箱旁边的监控按钮开始监听邮件
5. **自动提取**：系统会自动提取收到邮件中的验证码
6. **复制使用**：点击复制按钮复制邮箱地址或验证码

## 验证码提取

应用支持多种验证码格式的自动识别：

- 中文格式：验证码：123456
- 英文格式：Verification code: 123456
- 纯数字：4-8位数字
- 混合格式：字母数字组合
- HTML格式：`<strong>123456</strong>`

## 安全说明

- 临时邮箱仅用于临时验证，不要用于重要账户
- 自定义域名的IMAP密码建议使用应用专用密码
- 定期清理生成的临时邮箱
- 不要在临时邮箱中接收敏感信息

## 故障排除

### 无法生成 Mail.tm 邮箱

- 检查网络连接
- 确认 Mail.tm 服务可用
- 尝试重新启动应用

### IMAP 连接失败

- 检查 IMAP 设置是否正确
- 确认邮箱服务商支持 IMAP
- 检查防火墙设置
- 确认使用了正确的应用专用密码

### 验证码提取不准确

- 检查邮件内容格式
- 查看控制台日志了解详细信息
- 可以手动复制验证码

## 技术栈

- **Electron**: 桌面应用框架
- **Vue.js 3**: 前端框架
- **Node.js**: 后端运行时
- **node-imap**: IMAP 邮件处理
- **mailparser**: 邮件解析
- **axios**: HTTP 请求库

## 开发说明

### 项目结构

```
temp-email-generator/
├── main.js              # Electron 主进程
├── preload.js           # 预加载脚本
├── package.json         # 项目配置
├── email_config.json    # 邮箱配置
├── src/
│   ├── renderer/        # 渲染进程（前端）
│   │   ├── index.html
│   │   ├── style.css
│   │   └── app.js
│   ├── services/        # 核心服务
│   │   ├── emailGenerator.js
│   │   ├── imapListener.js
│   │   └── codeExtractor.js
│   └── utils/           # 工具类
│       └── config.js
└── assets/              # 资源文件
    └── icon.png
```

### 添加新的邮箱提供商

1. 在 `emailGenerator.js` 中添加新的生成方法
2. 更新配置文件中的 providers 列表
3. 在界面中添加新的选项

### 自定义验证码提取规则

在 `codeExtractor.js` 中的 `patterns` 数组中添加新的正则表达式模式。

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持 Mail.tm 和自定义域名邮箱
- 自动验证码提取功能
- 现代化用户界面
