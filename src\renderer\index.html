<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时邮箱生成器</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>
<body>
    <div id="app">
        <!-- 标题栏 -->
        <div class="header">
            <div class="title">
                <span class="icon">📧</span>
                邮箱生成器
            </div>
            <button class="close-btn" @click="closeApp">✕ 关闭</button>
        </div>

        <!-- 邮箱类型选择 -->
        <div class="email-type-section">
            <h3>选择邮箱类型:</h3>
            <div class="radio-group">
                <label class="radio-option" :class="{ active: selectedProvider === 'mail.tm' }">
                    <input type="radio" v-model="selectedProvider" value="mail.tm">
                    <div class="radio-content">
                        <div class="radio-title">Mail.tm</div>
                        <div class="radio-desc">临时邮箱，即时可用</div>
                    </div>
                </label>
                <label class="radio-option" :class="{ active: selectedProvider === 'custom_domain' }">
                    <input type="radio" v-model="selectedProvider" value="custom_domain">
                    <div class="radio-content">
                        <div class="radio-title">自定义域名</div>
                        <div class="radio-desc">使用配置的域名生成</div>
                    </div>
                </label>
            </div>
        </div>

        <!-- 生成数量选择 -->
        <div class="generate-section">
            <label for="count">生成数量:</label>
            <select id="count" v-model="generateCount">
                <option value="1">1个邮箱</option>
                <option value="3">3个邮箱</option>
                <option value="5">5个邮箱</option>
                <option value="10">10个邮箱</option>
            </select>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
            <button class="generate-btn" @click="generateEmails" :disabled="isGenerating">
                <span class="btn-icon">🔄</span>
                {{ isGenerating ? '生成中...' : '生成随机邮箱' }}
            </button>
            <button class="clear-btn" @click="clearResults">
                <span class="btn-icon">🗑️</span>
                清空结果
            </button>
        </div>

        <!-- 生成结果 -->
        <div class="results-section" v-if="generatedEmails.length > 0">
            <div class="section-header">
                <h3>生成结果</h3>
                <button class="copy-all-btn" @click="copyAllEmails">📋 复制全部</button>
            </div>
            <div class="email-list">
                <div v-for="(email, index) in generatedEmails" :key="index" class="email-item">
                    <div class="email-address">{{ email.address }}</div>
                    <div class="email-provider">{{ email.provider }}</div>
                    <div class="email-actions">
                        <button class="copy-btn" @click="copyEmail(email.address)">📋</button>
                        <button class="monitor-btn" @click="startMonitoring(email.address)" 
                                :class="{ active: monitoringEmail === email.address }">
                            {{ monitoringEmail === email.address ? '●' : '○' }}
                        </button>
                        <button class="check-btn" @click="checkEmails(email.address)">✓</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 收件箱监控 -->
        <div class="inbox-section">
            <div class="section-header">
                <h3>📬 收件箱监控</h3>
                <div class="status-indicator" :class="{ active: isMonitoring }">
                    {{ isMonitoring ? '🟢 等待验证码...' : '⚪ 停止监控' }}
                </div>
                <button class="stop-monitor-btn" @click="stopMonitoring" v-if="isMonitoring">停止监控</button>
            </div>
            
            <div class="current-email" v-if="monitoringEmail">
                <label>当前监听邮箱:</label>
                <div class="email-display">{{ monitoringEmail }}</div>
            </div>

            <div class="inbox-content">
                <div v-if="!isMonitoring && !monitoringEmail" class="empty-state">
                    <div class="empty-icon">📭</div>
                    <div class="empty-text">选择一个邮箱开始监控</div>
                </div>
                
                <div v-else-if="isMonitoring" class="monitoring-state">
                    <div class="monitoring-icon">📡</div>
                    <div class="monitoring-text">正在自动监听邮箱，等待验证码到达...</div>
                    <div class="monitoring-desc">系统将自动检测并提取验证码，无需手动操作</div>
                    <div class="monitoring-status">准备开始检查...</div>
                    
                    <div class="features">
                        <div class="feature-item">
                            <span class="feature-icon">🔄</span>
                            每3秒自动检查新邮件
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">🔍</span>
                            智能识别验证码内容
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">📋</span>
                            自动复制到剪贴板
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">⚡</span>
                            实时状态更新
                        </div>
                    </div>
                </div>

                <!-- 邮件列表 -->
                <div v-if="emails.length > 0" class="email-messages">
                    <div v-for="email in emails" :key="email.id" class="message-item">
                        <div class="message-header">
                            <div class="message-subject">{{ email.subject }}</div>
                            <div class="message-time">{{ formatTime(email.date) }}</div>
                        </div>
                        <div class="message-from">来自: {{ email.from }}</div>
                        <div v-if="email.verificationCode" class="verification-code">
                            <span class="code-label">验证码:</span>
                            <span class="code-value">{{ email.verificationCode }}</span>
                            <button class="copy-code-btn" @click="copyCode(email.verificationCode)">复制</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
