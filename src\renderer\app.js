const { createApp } = Vue;

createApp({
  data() {
    return {
      selectedProvider: 'mail.tm',
      generateCount: 1,
      isGenerating: false,
      generatedEmails: [],
      monitoringEmail: '',
      isMonitoring: false,
      emails: [],
      checkInterval: null
    };
  },
  
  mounted() {
    // 设置邮件监听器
    window.electronAPI.setupEmailListener();
    
    // 监听新邮件
    window.electronAPI.onNewEmail((event, email) => {
      this.emails.unshift(email);
      this.showNotification('收到新邮件', email.subject);
    });
    
    // 监听验证码
    window.electronAPI.onVerificationCode((event, code) => {
      this.copyToClipboard(code);
      this.showNotification('验证码已复制', `验证码: ${code}`);
    });
  },
  
  beforeUnmount() {
    // 清理监听器
    window.electronAPI.removeAllListeners('new-email-received');
    window.electronAPI.removeAllListeners('verification-code-extracted');
    
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }
  },
  
  methods: {
    async generateEmails() {
      this.isGenerating = true;
      try {
        const options = {
          provider: this.selectedProvider,
          count: parseInt(this.generateCount)
        };
        
        const result = await window.electronAPI.generateEmail(options);
        
        if (result.success) {
          this.generatedEmails = result.emails;
          this.showNotification('生成成功', `已生成 ${result.emails.length} 个邮箱`);
        } else {
          this.showError('生成失败', result.error);
        }
      } catch (error) {
        console.error('生成邮箱失败:', error);
        this.showError('生成失败', error.message);
      } finally {
        this.isGenerating = false;
      }
    },
    
    clearResults() {
      this.generatedEmails = [];
      this.emails = [];
      this.stopMonitoring();
    },
    
    async startMonitoring(emailAddress) {
      try {
        // 停止之前的监控
        if (this.isMonitoring) {
          await this.stopMonitoring();
        }
        
        this.monitoringEmail = emailAddress;
        this.isMonitoring = true;
        this.emails = [];
        
        // 开始监控
        const result = await window.electronAPI.startEmailMonitoring(emailAddress);
        
        if (result.success) {
          this.showNotification('开始监控', `正在监控邮箱: ${emailAddress}`);
          
          // 定期检查邮件
          this.checkInterval = setInterval(async () => {
            await this.checkEmails(emailAddress);
          }, 3000); // 每3秒检查一次
          
        } else {
          this.showError('监控失败', result.error);
          this.isMonitoring = false;
          this.monitoringEmail = '';
        }
      } catch (error) {
        console.error('开始监控失败:', error);
        this.showError('监控失败', error.message);
        this.isMonitoring = false;
        this.monitoringEmail = '';
      }
    },
    
    async stopMonitoring() {
      try {
        if (this.checkInterval) {
          clearInterval(this.checkInterval);
          this.checkInterval = null;
        }
        
        if (this.isMonitoring) {
          await window.electronAPI.stopEmailMonitoring();
          this.showNotification('停止监控', '已停止邮件监控');
        }
        
        this.isMonitoring = false;
        this.monitoringEmail = '';
      } catch (error) {
        console.error('停止监控失败:', error);
      }
    },
    
    async checkEmails(emailAddress) {
      try {
        const result = await window.electronAPI.getEmails();
        
        if (result.success && result.emails) {
          // 检查是否有新邮件
          const newEmails = result.emails.filter(email => 
            !this.emails.some(existing => existing.id === email.id)
          );
          
          for (const email of newEmails) {
            // 提取验证码
            const codeResult = await window.electronAPI.extractVerificationCode(email.text || email.html);
            if (codeResult.success && codeResult.code) {
              email.verificationCode = codeResult.code;
              this.copyToClipboard(codeResult.code);
              this.showNotification('发现验证码', `验证码: ${codeResult.code}`);
            }
            
            this.emails.unshift(email);
          }
        }
      } catch (error) {
        console.error('检查邮件失败:', error);
      }
    },
    
    copyEmail(email) {
      this.copyToClipboard(email);
      this.showNotification('已复制', email);
    },
    
    copyAllEmails() {
      const emailList = this.generatedEmails.map(email => email.address).join('\n');
      this.copyToClipboard(emailList);
      this.showNotification('已复制', `已复制 ${this.generatedEmails.length} 个邮箱`);
    },
    
    copyCode(code) {
      this.copyToClipboard(code);
      this.showNotification('验证码已复制', code);
    },
    
    copyToClipboard(text) {
      navigator.clipboard.writeText(text).catch(err => {
        console.error('复制失败:', err);
        // 备用复制方法
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
      });
    },
    
    formatTime(date) {
      if (!date) return '';
      const d = new Date(date);
      return d.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    
    showNotification(title, message) {
      // 简单的通知显示
      console.log(`${title}: ${message}`);
      
      // 可以添加更好的通知UI
      const notification = document.createElement('div');
      notification.className = 'notification';
      notification.innerHTML = `
        <div class="notification-title">${title}</div>
        <div class="notification-message">${message}</div>
      `;
      
      // 添加通知样式
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #27ae60;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        z-index: 1000;
        max-width: 300px;
        animation: slideIn 0.3s ease-out;
      `;
      
      document.body.appendChild(notification);
      
      // 3秒后自动移除
      setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        }, 300);
      }, 3000);
    },
    
    showError(title, message) {
      console.error(`${title}: ${message}`);
      
      const notification = document.createElement('div');
      notification.className = 'notification error';
      notification.innerHTML = `
        <div class="notification-title">${title}</div>
        <div class="notification-message">${message}</div>
      `;
      
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #e74c3c;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        z-index: 1000;
        max-width: 300px;
        animation: slideIn 0.3s ease-out;
      `;
      
      document.body.appendChild(notification);
      
      setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        }, 300);
      }, 5000);
    },
    
    closeApp() {
      window.close();
    }
  }
}).mount('#app');

// 添加通知动画样式
const style = document.createElement('style');
style.textContent = `
  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes slideOut {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
  
  .notification-title {
    font-weight: 600;
    margin-bottom: 5px;
  }
  
  .notification-message {
    font-size: 14px;
    opacity: 0.9;
  }
`;
document.head.appendChild(style);
