const fs = require('fs');
const path = require('path');

class ConfigManager {
  constructor() {
    this.configPath = path.join(__dirname, '../../email_config.json');
    this.config = null;
    this.loadConfig();
  }

  loadConfig() {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf8');
        this.config = JSON.parse(configData);
        console.log('配置文件加载成功');
      } else {
        console.warn('配置文件不存在，使用默认配置');
        this.config = this.getDefaultConfig();
        this.saveConfig();
      }
    } catch (error) {
      console.error('加载配置文件失败:', error);
      this.config = this.getDefaultConfig();
    }
  }

  getDefaultConfig() {
    return {
      "service_type": "temp_email",
      "custom_domain": {
        "domain": "example.com",
        "real_email": "<EMAIL>",
        "forwarding_service": "cloudflare",
        "updated_at": new Date().toISOString(),
        "imap_settings": {
          "server": "imap.example.com",
          "port": 993,
          "use_ssl": true,
          "username": "<EMAIL>",
          "password": "your-app-password",
          "check_interval": 30,
          "search_days": 1,
          "max_emails": 10
        }
      },
      "temp_email": {
        "providers": [
          {
            "name": "mail.tm",
            "enabled": true,
            "priority": 1,
            "display_name": "Mail.tm",
            "description": "稳定的临时邮箱服务"
          },
          {
            "name": "custom_domain",
            "enabled": true,
            "priority": 2,
            "display_name": "自定义域名",
            "description": "使用自定义域名邮箱"
          }
        ],
        "enabled": true,
        "rotation_enabled": true,
        "auto_switch": true,
        "preferred_provider": "auto"
      },
      "email_patterns": {
        "prefixes": ["dev", "test", "user", "admin", "support", "info", "contact", "hello", "team"],
        "use_realistic_names": true,
        "add_year_suffix": true,
        "avoid_repetition": true
      }
    };
  }

  saveConfig() {
    try {
      const configData = JSON.stringify(this.config, null, 2);
      fs.writeFileSync(this.configPath, configData, 'utf8');
      console.log('配置文件保存成功');
      return true;
    } catch (error) {
      console.error('保存配置文件失败:', error);
      return false;
    }
  }

  getConfig() {
    return this.config;
  }

  updateConfig(newConfig) {
    try {
      this.config = { ...this.config, ...newConfig };
      return this.saveConfig();
    } catch (error) {
      console.error('更新配置失败:', error);
      return false;
    }
  }

  // 获取邮箱提供商配置
  getProviders() {
    return this.config?.temp_email?.providers || [];
  }

  // 获取启用的提供商
  getEnabledProviders() {
    return this.getProviders().filter(provider => provider.enabled);
  }

  // 获取自定义域名配置
  getCustomDomainConfig() {
    return this.config?.custom_domain || null;
  }

  // 获取IMAP设置
  getImapSettings() {
    return this.config?.custom_domain?.imap_settings || null;
  }

  // 获取邮箱模式配置
  getEmailPatterns() {
    return this.config?.email_patterns || {};
  }

  // 更新IMAP设置
  updateImapSettings(imapSettings) {
    if (!this.config.custom_domain) {
      this.config.custom_domain = {};
    }
    this.config.custom_domain.imap_settings = { ...this.config.custom_domain.imap_settings, ...imapSettings };
    return this.saveConfig();
  }

  // 更新自定义域名设置
  updateCustomDomain(domainConfig) {
    this.config.custom_domain = { ...this.config.custom_domain, ...domainConfig };
    return this.saveConfig();
  }

  // 更新邮箱模式
  updateEmailPatterns(patterns) {
    this.config.email_patterns = { ...this.config.email_patterns, ...patterns };
    return this.saveConfig();
  }

  // 验证配置
  validateConfig() {
    const errors = [];

    // 验证基本结构
    if (!this.config) {
      errors.push('配置文件为空');
      return errors;
    }

    // 验证自定义域名配置
    if (this.config.custom_domain) {
      const customDomain = this.config.custom_domain;
      
      if (!customDomain.domain) {
        errors.push('自定义域名未设置');
      }
      
      if (!customDomain.real_email) {
        errors.push('真实邮箱地址未设置');
      }

      // 验证IMAP设置
      if (customDomain.imap_settings) {
        const imap = customDomain.imap_settings;
        
        if (!imap.server) {
          errors.push('IMAP服务器地址未设置');
        }
        
        if (!imap.username) {
          errors.push('IMAP用户名未设置');
        }
        
        if (!imap.password) {
          errors.push('IMAP密码未设置');
        }
        
        if (!imap.port || imap.port < 1 || imap.port > 65535) {
          errors.push('IMAP端口设置无效');
        }
      }
    }

    // 验证提供商配置
    if (this.config.temp_email && this.config.temp_email.providers) {
      const enabledProviders = this.getEnabledProviders();
      if (enabledProviders.length === 0) {
        errors.push('没有启用的邮箱提供商');
      }
    }

    return errors;
  }

  // 获取配置状态
  getConfigStatus() {
    const errors = this.validateConfig();
    
    return {
      isValid: errors.length === 0,
      errors: errors,
      hasCustomDomain: !!(this.config?.custom_domain?.domain),
      hasImapSettings: !!(this.config?.custom_domain?.imap_settings?.server),
      enabledProviders: this.getEnabledProviders().length
    };
  }

  // 重置配置为默认值
  resetToDefault() {
    this.config = this.getDefaultConfig();
    return this.saveConfig();
  }

  // 备份当前配置
  backupConfig() {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = path.join(path.dirname(this.configPath), `email_config_backup_${timestamp}.json`);
      
      const configData = JSON.stringify(this.config, null, 2);
      fs.writeFileSync(backupPath, configData, 'utf8');
      
      console.log(`配置已备份到: ${backupPath}`);
      return backupPath;
    } catch (error) {
      console.error('备份配置失败:', error);
      return null;
    }
  }
}

module.exports = ConfigManager;
