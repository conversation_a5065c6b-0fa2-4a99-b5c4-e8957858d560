* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: #333;
    overflow-x: hidden;
}

#app {
    max-width: 100%;
    margin: 0;
    padding: 20px;
    min-height: 100vh;
}

/* 标题栏 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 15px 20px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.title {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.title .icon {
    margin-right: 8px;
    font-size: 20px;
}

.close-btn {
    background: #ff5757;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.3s;
}

.close-btn:hover {
    background: #ff4757;
}

/* 邮箱类型选择 */
.email-type-section {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.email-type-section h3 {
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 16px;
}

.radio-group {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.radio-option {
    flex: 1;
    min-width: 200px;
    border: 2px solid #e1e8ed;
    border-radius: 10px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s;
    background: #f8f9fa;
}

.radio-option:hover {
    border-color: #3498db;
    background: #f0f8ff;
}

.radio-option.active {
    border-color: #3498db;
    background: #e3f2fd;
}

.radio-option input[type="radio"] {
    display: none;
}

.radio-content {
    text-align: left;
}

.radio-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.radio-desc {
    font-size: 14px;
    color: #7f8c8d;
}

/* 生成数量选择 */
.generate-section {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.generate-section label {
    font-weight: 600;
    color: #2c3e50;
}

.generate-section select {
    padding: 10px 15px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    cursor: pointer;
    transition: border-color 0.3s;
}

.generate-section select:focus {
    outline: none;
    border-color: #3498db;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.generate-btn, .clear-btn {
    flex: 1;
    padding: 15px 20px;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.generate-btn {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.generate-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
}

.generate-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.clear-btn {
    background: #95a5a6;
    color: white;
}

.clear-btn:hover {
    background: #7f8c8d;
    transform: translateY(-2px);
}

/* 生成结果 */
.results-section {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.section-header h3 {
    color: #2c3e50;
    font-size: 16px;
}

.copy-all-btn {
    background: #27ae60;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s;
}

.copy-all-btn:hover {
    background: #229954;
}

.email-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.email-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e1e8ed;
}

.email-address {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: #2c3e50;
    font-weight: 500;
}

.email-provider {
    padding: 4px 8px;
    background: #e3f2fd;
    color: #1976d2;
    border-radius: 4px;
    font-size: 12px;
    margin-right: 10px;
}

.email-actions {
    display: flex;
    gap: 5px;
}

.copy-btn, .monitor-btn, .check-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.copy-btn {
    background: #f39c12;
    color: white;
}

.copy-btn:hover {
    background: #e67e22;
}

.monitor-btn {
    background: #95a5a6;
    color: white;
}

.monitor-btn:hover {
    background: #7f8c8d;
}

.monitor-btn.active {
    background: #e74c3c;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.check-btn {
    background: #27ae60;
    color: white;
}

.check-btn:hover {
    background: #229954;
}

/* 收件箱监控 */
.inbox-section {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    color: #7f8c8d;
}

.status-indicator.active {
    color: #27ae60;
}

.stop-monitor-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
}

.current-email {
    margin: 15px 0;
    padding: 15px;
    background: #e8f4fd;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.current-email label {
    font-weight: 600;
    color: #2c3e50;
    display: block;
    margin-bottom: 5px;
}

.email-display {
    font-family: 'Courier New', monospace;
    color: #3498db;
    font-size: 14px;
}

.inbox-content {
    min-height: 200px;
    border: 2px dashed #e1e8ed;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 160px;
    color: #7f8c8d;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 10px;
}

.empty-text {
    font-size: 16px;
}

.monitoring-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #27ae60;
}

.monitoring-icon {
    font-size: 48px;
    margin-bottom: 15px;
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.monitoring-text {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
}

.monitoring-desc {
    font-size: 14px;
    color: #7f8c8d;
    margin-bottom: 20px;
}

.monitoring-status {
    padding: 8px 15px;
    background: #d4edda;
    color: #155724;
    border-radius: 6px;
    font-size: 14px;
    margin-bottom: 20px;
}

.features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-top: 20px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #2c3e50;
}

.feature-icon {
    font-size: 16px;
}

/* 邮件消息 */
.email-messages {
    margin-top: 20px;
}

.message-item {
    background: #f8f9fa;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    text-align: left;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.message-subject {
    font-weight: 600;
    color: #2c3e50;
}

.message-time {
    font-size: 12px;
    color: #7f8c8d;
}

.message-from {
    font-size: 14px;
    color: #7f8c8d;
    margin-bottom: 10px;
}

.verification-code {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
}

.code-label {
    font-weight: 600;
    color: #856404;
}

.code-value {
    font-family: 'Courier New', monospace;
    font-size: 16px;
    font-weight: bold;
    color: #d63031;
    background: white;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.copy-code-btn {
    background: #f39c12;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.copy-code-btn:hover {
    background: #e67e22;
}
