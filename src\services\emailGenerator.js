const axios = require('axios');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

class EmailGenerator {
  constructor() {
    this.config = this.loadConfig();
    this.mailTmToken = null;
    this.mailTmDomains = [];
  }

  loadConfig() {
    try {
      const configPath = path.join(__dirname, '../../email_config.json');
      const configData = fs.readFileSync(configPath, 'utf8');
      return JSON.parse(configData);
    } catch (error) {
      console.error('加载配置文件失败:', error);
      return null;
    }
  }

  async generateEmail(options) {
    try {
      const { provider, count } = options;
      const emails = [];

      for (let i = 0; i < count; i++) {
        let email;
        
        if (provider === 'mail.tm') {
          email = await this.generateMailTmEmail();
        } else if (provider === 'custom_domain') {
          email = await this.generateCustomDomainEmail();
        } else {
          throw new Error('不支持的邮箱提供商');
        }

        if (email) {
          emails.push(email);
        }
      }

      return {
        success: true,
        emails: emails
      };
    } catch (error) {
      console.error('生成邮箱失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async generateMailTmEmail() {
    try {
      // 获取可用域名
      if (this.mailTmDomains.length === 0) {
        await this.getMailTmDomains();
      }

      if (this.mailTmDomains.length === 0) {
        throw new Error('无法获取Mail.tm域名');
      }

      // 生成随机用户名
      const username = this.generateRandomUsername();
      const domain = this.mailTmDomains[Math.floor(Math.random() * this.mailTmDomains.length)];
      const emailAddress = `${username}@${domain}`;
      const password = this.generateRandomPassword();

      // 创建账户
      const accountResponse = await axios.post('https://api.mail.tm/accounts', {
        address: emailAddress,
        password: password
      });

      if (accountResponse.status === 201) {
        // 获取访问令牌
        const tokenResponse = await axios.post('https://api.mail.tm/token', {
          address: emailAddress,
          password: password
        });

        return {
          address: emailAddress,
          password: password,
          token: tokenResponse.data.token,
          provider: 'Mail.tm临时邮箱',
          id: accountResponse.data.id
        };
      }
    } catch (error) {
      console.error('生成Mail.tm邮箱失败:', error);
      throw new Error(`Mail.tm邮箱生成失败: ${error.response?.data?.message || error.message}`);
    }
  }

  async getMailTmDomains() {
    try {
      const response = await axios.get('https://api.mail.tm/domains');
      this.mailTmDomains = response.data['hydra:member'].map(domain => domain.domain);
    } catch (error) {
      console.error('获取Mail.tm域名失败:', error);
      throw new Error('无法获取Mail.tm域名列表');
    }
  }

  async generateCustomDomainEmail() {
    try {
      if (!this.config || !this.config.custom_domain) {
        throw new Error('自定义域名配置不存在');
      }

      const { domain } = this.config.custom_domain;
      const username = this.generateRealisticUsername();
      const emailAddress = `${username}@${domain}`;

      return {
        address: emailAddress,
        provider: '自定义域名',
        domain: domain,
        forwardTo: this.config.custom_domain.real_email
      };
    } catch (error) {
      console.error('生成自定义域名邮箱失败:', error);
      throw new Error(`自定义域名邮箱生成失败: ${error.message}`);
    }
  }

  generateRandomUsername() {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let username = '';
    
    // 生成6-12位随机用户名
    const length = Math.floor(Math.random() * 7) + 6;
    
    for (let i = 0; i < length; i++) {
      username += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return username;
  }

  generateRealisticUsername() {
    if (!this.config || !this.config.email_patterns) {
      return this.generateRandomUsername();
    }

    const { prefixes, use_realistic_names, add_year_suffix, avoid_repetition } = this.config.email_patterns;
    
    let username = '';
    
    if (use_realistic_names && prefixes && prefixes.length > 0) {
      // 使用配置的前缀
      const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
      username = prefix;
      
      // 添加随机数字
      const randomNum = Math.floor(Math.random() * 9999) + 1;
      username += randomNum;
      
      // 可选：添加年份后缀
      if (add_year_suffix && Math.random() > 0.5) {
        const currentYear = new Date().getFullYear();
        username += currentYear;
      }
    } else {
      username = this.generateRandomUsername();
    }
    
    return username;
  }

  generateRandomPassword() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return password;
  }

  // 获取Mail.tm邮箱的邮件
  async getMailTmEmails(token) {
    try {
      const response = await axios.get('https://api.mail.tm/messages', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      return response.data['hydra:member'] || [];
    } catch (error) {
      console.error('获取Mail.tm邮件失败:', error);
      return [];
    }
  }

  // 获取Mail.tm邮件详情
  async getMailTmEmailDetail(messageId, token) {
    try {
      const response = await axios.get(`https://api.mail.tm/messages/${messageId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      return response.data;
    } catch (error) {
      console.error('获取Mail.tm邮件详情失败:', error);
      return null;
    }
  }
}

module.exports = EmailGenerator;
