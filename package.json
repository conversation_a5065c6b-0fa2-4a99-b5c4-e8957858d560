{"name": "temp-email-generator", "version": "1.0.0", "description": "临时邮箱生成器 - 桌面版", "main": "main.js", "scripts": {"start": "electron .", "dev": "concurrently \"npm run start\"", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux"}, "keywords": ["temp-email", "temporary-email", "email-generator", "verification-code"], "author": "Your Name", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1", "concurrently": "^8.2.2"}, "dependencies": {"axios": "^1.6.2", "node-imap": "^0.9.6", "mailparser": "^3.6.5", "vue": "^3.3.8"}, "build": {"appId": "com.tempmail.generator", "productName": "临时邮箱生成器", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "src/**/*", "email_config.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}