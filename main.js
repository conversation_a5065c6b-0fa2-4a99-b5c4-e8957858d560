const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const EmailGenerator = require('./src/services/emailGenerator');
const ImapListener = require('./src/services/imapListener');
const CodeExtractor = require('./src/services/codeExtractor');

let mainWindow;
let emailGenerator;
let imapListener;
let codeExtractor;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 900,
    height: 700,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets/icon.png'),
    title: '临时邮箱生成器',
    resizable: true,
    minimizable: true,
    maximizable: true
  });

  mainWindow.loadFile('src/renderer/index.html');

  // 开发模式下打开开发者工具
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }
}

app.whenReady().then(() => {
  createWindow();
  
  // 初始化服务
  emailGenerator = new EmailGenerator();
  imapListener = new ImapListener();
  codeExtractor = new CodeExtractor();

  app.on('activate', function () {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

// IPC 处理程序
ipcMain.handle('generate-email', async (event, options) => {
  try {
    return await emailGenerator.generateEmail(options);
  } catch (error) {
    console.error('生成邮箱失败:', error);
    throw error;
  }
});

ipcMain.handle('start-email-monitoring', async (event, emailAddress) => {
  try {
    return await imapListener.startMonitoring(emailAddress);
  } catch (error) {
    console.error('开始邮件监控失败:', error);
    throw error;
  }
});

ipcMain.handle('stop-email-monitoring', async (event) => {
  try {
    return await imapListener.stopMonitoring();
  } catch (error) {
    console.error('停止邮件监控失败:', error);
    throw error;
  }
});

ipcMain.handle('get-emails', async (event) => {
  try {
    return await imapListener.getEmails();
  } catch (error) {
    console.error('获取邮件失败:', error);
    throw error;
  }
});

ipcMain.handle('extract-verification-code', async (event, emailContent) => {
  try {
    return codeExtractor.extractCode(emailContent);
  } catch (error) {
    console.error('提取验证码失败:', error);
    throw error;
  }
});

// 邮件监听事件
ipcMain.on('setup-email-listener', (event) => {
  imapListener.on('new-email', (email) => {
    event.reply('new-email-received', email);
  });
  
  imapListener.on('verification-code', (code) => {
    event.reply('verification-code-extracted', code);
  });
});
